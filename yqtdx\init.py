#!/usr/bin/env python3
"""
全量转换TDX数据到Parquet格式
直接运行: python init.py
注意：需要先运行 python download_init_data.py 下载数据
"""
import time
from pathlib import Path
import sys

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from tdx.converter import TDXConverter
from utils.config import load_config


def main():
    """主函数"""
    # 加载配置（从脚本所在目录查找）
    script_dir = Path(__file__).parent
    config_path = script_dir / "config.toml"
    
    try:
        config = load_config(str(config_path))
        print(f"📋 配置加载成功")
        print(f"   TDX全量数据文件目录: {config.tdx_day_path}")
        print(f"   输出目录: {config.output_dir}")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return 1
    
    # 检查day文件是否存在
    tdx_path = Path(config.tdx_day_path)
    day_files = list(tdx_path.rglob("*.day")) if tdx_path.exists() else []
    
    if not day_files:
        print("请先去https://www.tdx.com.cn/article/alldata.html下载通达信全量数据(上证日线,深证日线,北证日线,指数日线)")
        return 1
    
    # 创建输出目录
    Path(config.output_dir).mkdir(parents=True, exist_ok=True)
    
    # 转换器
    converter = TDXConverter(config)
    
    start_time = time.time()
    try:
        output_dir = converter.convert_to_individual_parquets(config.tdx_day_path)
        
        elapsed = time.time() - start_time
        print(f"✅ 转换完成，耗时 {elapsed:.2f}s")
        print(f"📁 输出目录: {output_dir}")
        
        return 0
        
    except Exception as e:
        print(f"🛑 错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    exit(main())