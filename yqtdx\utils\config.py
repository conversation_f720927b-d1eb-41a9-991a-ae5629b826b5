from dataclasses import dataclass
from typing import List
from pathlib import Path


@dataclass
class Config:
    """项目配置"""
    # 路径设置
    work_dir: str  # 工作目录（所有操作都在其子目录）
    
    # 输出设置
    partitioned: bool = False
    
    # 股票代码前缀
    valid_prefixes: List[str] = None
    
    # 性能参数
    max_workers: int = 16
    
    @property
    def tdx_day_path(self) -> str:
        """TDX day文件目录"""
        return str(Path(self.work_dir) / "day_files")
    
    @property
    def output_dir(self) -> str:
        """输出目录"""
        return str(Path(self.work_dir) / "output")
    
    @property
    def download_dir(self) -> str:
        """下载临时目录"""
        return str(Path(self.work_dir) / "downloads")


def load_config(config_file: str = "config.toml") -> Config:
    """从配置文件加载配置"""
    try:
        import tomllib  # Python 3.11+
    except ImportError:
        import tomli as tomllib  # Python < 3.11, 需要安装 tomli
    
    # 读取配置文件
    config_path = Path(config_file)
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_file}")
    
    with open(config_path, 'rb') as f:
        data = tomllib.load(f)
    
    # 解析配置
    config = Config(
        work_dir=data['paths']['work_dir'],
        partitioned=data['output']['partitioned'],
        max_workers=data['performance']['max_workers'],
        valid_prefixes=data['stocks']['valid_prefixes']
    )
    
    return config