import struct
import os
from datetime import datetime
from typing import List
import polars as pl
from joblib import Parallel, delayed
from pathlib import Path
import numpy as np

# Day文件记录结构（32字节）
DAY_RECORD_FORMAT = '<IIIIIfI'  # 小端字节序：日期(4) + OHLC(各4) + 成交额(4) + 成交量(4)
RECORD_SIZE = 32


def parse_day_file_numpy(file_path: str) -> pl.DataFrame:
    """使用numpy和polars高效解析Day文件"""
    # 定义numpy数据类型 (TDX格式是32字节，8个字段)
    dtype = np.dtype([
        ('date', '<u4'),      # 4字节: 日期 YYYYMMDD
        ('open', '<u4'),      # 4字节: 开盘价 * 100
        ('high', '<u4'),      # 4字节: 最高价 * 100
        ('low', '<u4'),       # 4字节: 最低价 * 100
        ('close', '<u4'),     # 4字节: 收盘价 * 100
        ('amount', '<f4'),    # 4字节: 成交额 float32
        ('volume', '<u4'),    # 4字节: 成交量
        ('reserved', '<u4')   # 4字节: 保留字段
    ])
    
    # 一次性读取整个文件
    data = np.fromfile(file_path, dtype=dtype)
    
    if len(data) == 0:
        return pl.DataFrame()
    
    # 获取股票代码
    symbol = Path(file_path).stem
    
    # 转换为polars DataFrame
    df = pl.DataFrame({
        'symbol': symbol,
        'date': data['date'],
        'open': data['open'] / 100,
        'high': data['high'] / 100,
        'low': data['low'] / 100,
        'close': data['close'] / 100,
        'amount': data['amount'],
        'volume': data['volume']
    })
    
    # 直接转换日期格式（现在格式定义正确了，不需要额外验证）
    df = df.with_columns([
        ((pl.col('date') // 10000).cast(pl.Utf8) + '-' +
         ((pl.col('date') % 10000) // 100).cast(pl.Utf8).str.zfill(2) + '-' +
         (pl.col('date') % 100).cast(pl.Utf8).str.zfill(2)).str.strptime(pl.Date, "%Y-%m-%d").alias('date')
    ])
    
    return df


def convert_single_day_file_to_parquet(day_file_path: str, output_dir: str) -> str:
    """转换单个day文件为独立的parquet文件"""
    day_file_path = Path(day_file_path)
    output_dir = Path(output_dir)
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 解析day文件
    df = parse_day_file_numpy(str(day_file_path))
    
    if df.is_empty():
        print(f"跳过空文件: {day_file_path}")
        return ""
    
    # 保存为parquet文件，文件名为股票代码
    output_file = output_dir / f"{day_file_path.stem}.parquet"
    df.write_parquet(str(output_file), compression='zstd', compression_level=3)
    
    return str(output_file)


def convert_all_day_files_individually(day_dir: str, valid_prefixes: List[str], output_dir: str,
                                      max_workers: int = 16) -> int:
    """转换所有day文件为独立的parquet文件（按股票分别保存）
    
    Args:
        day_dir: TDX day文件目录
        valid_prefixes: 有效的股票代码前缀 ['sh', 'sz', 'bj']
        output_dir: 输出目录
        max_workers: 并行线程数 (建议32-64, I/O密集型任务)
    
    Returns:
        成功转换的文件数量
    """
    # 收集需要处理的文件
    day_files = []
    for root, _, files in os.walk(day_dir):
        for file in files:
            if file.endswith('.day'):
                symbol = file[:-4]
                if any(symbol.startswith(prefix) for prefix in valid_prefixes):
                    day_files.append(os.path.join(root, file))
    
    if not day_files:
        raise ValueError(f"No valid .day files found in {day_dir}")
    
    # 使用joblib并发处理文件
    def process_file_with_error_handling(file_path):
        try:
            return convert_single_day_file_to_parquet(file_path, output_dir)
        except Exception as e:
            print(f"❌ {Path(file_path).name}: {e}")
            return ""
    
    # I/O密集型任务优化配置：
    # - backend='threading': 适合I/O密集型，避免进程间通信开销
    # - verbose=1: 显示进度条
    # - batch_size='auto': 自动批处理优化
    results = Parallel(
        n_jobs=max_workers,
        backend='threading',    # I/O密集型最佳选择
        verbose=0,              # 显示进度
        batch_size='auto'       # 自动优化批处理
    )(delayed(process_file_with_error_handling)(f) for f in day_files)
    
    # 统计成功转换的文件数
    success_count = len([r for r in results if r])
    
    return success_count

