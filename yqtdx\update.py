#!/usr/bin/env python3
"""
增量更新TDX数据
直接运行: python update.py
"""
import time
from pathlib import Path
from datetime import datetime, timedelta
import sys

# 添加当前目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from tdx.converter import TDXConverter
from utils.config import load_config


def get_latest_date(output_dir: str, config) -> datetime:
    """从文件名获取最新日期"""
    converter = TDXConverter(config)
    return converter.get_latest_date_from_files(output_dir)


def main():
    """主函数"""
    # 加载配置（从脚本所在目录查找）
    script_dir = Path(__file__).parent
    config_path = script_dir / "config.toml"
    
    try:
        config = load_config(str(config_path))
        print(f"📋 配置加载成功")
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return 1
    
    start_time = time.time()
    try:
        # 检查输出目录是否存在
        output_path = Path(config.output_dir)
        if not output_path.exists():
            print("❌ 未找到输出目录，请先运行 init.py 进行初始化")
            return 1
        
        # 获取最新日期（从文件名）
        try:
            latest_date = get_latest_date(str(output_path), config)
            print(f"📅 当前数据最新日期: {latest_date.strftime('%Y-%m-%d')}")
        except Exception as e:
            print(f"❌ 获取最新日期失败: {e}")
            return 1
        
        # 判断是否需要更新
        today = datetime.now().date()
        latest_date_only = latest_date.date()  # 转换为date对象
        if latest_date_only >= today - timedelta(days=1):
            print("✅ 数据已是最新，无需更新")
            return 0
        
        print("🔄 开始增量更新...")
        
        # 创建转换器实例
        converter = TDXConverter(config)
        
        # TODO: 增量更新逻辑（需要下载功能支持）
        # 当前实现：处理TDX数据目录中新的day文件
        print("📂 正在扫描TDX数据目录中的新文件...")
        
        # 检查是否有新的day文件需要处理
        tdx_path = Path(config.tdx_day_path)
        if not tdx_path.exists():
            print("❌ TDX数据目录不存在，请检查配置或手动下载数据")
            return 1
        
        # 计算需要下载的日期范围
        next_date = latest_date + timedelta(days=1)
        
        if next_date.date() > today:
            print("✅ 数据已是最新，无需更新")
            return 0
        
        # TODO: 实现增量下载和处理
        print(f"📥 需要下载 {next_date.strftime('%Y-%m-%d')} 到 {today.strftime('%Y-%m-%d')} 的数据")
        print("⚠️  当前版本下载功能待完善")
        print("   请手动下载新数据到TDX目录，然后重新运行")
        
        # 处理现有的新数据（如果有）
        print("📊 处理现有新数据...")
        if config.partitioned:
            output_path = converter.convert_to_partitioned_parquet(config.tdx_day_path)
            print(f"✅ 输出目录: {output_path}")
        else:
            output_file = converter.convert_to_single_parquet(config.tdx_day_path)
            print(f"✅ 输出文件: {output_file}")
        
        elapsed = time.time() - start_time
        print(f"✅ 更新完成，耗时 {elapsed:.2f}s")
        return 0
        
    except Exception as e:
        print(f"🛑 错误: {e}")
        return 1


if __name__ == '__main__':
    exit(main())