# YQTDX - 通达信数据转换器

一个高性能的通达信(TDX)股票数据转换工具，将二进制DAY文件转换为现代化的Parquet格式，支持按日期分区存储。

## 功能特点

- 🚀 **高性能**：使用Polars和并行处理，比pandas快10倍以上
- 📅 **按日期分区**：每个文件包含一个交易日的所有证券数据
- 🗜️ **高效压缩**：使用zstd压缩，节省存储空间
- 🔄 **增量更新**：支持增量处理新数据
- ⚙️ **灵活配置**：TOML配置文件，支持多种自定义选项

## 快速开始

### 1. 安装依赖

```bash
pip install polars pyarrow numpy toml joblib
```

### 2. 配置设置

编辑 `config.toml` 文件：

```toml
[paths]
work_dir = "./data"  # 工作目录（所有数据都在此目录的子目录）

[output]
partitioned = true  # 启用按日期分区（推荐）

[performance]
max_workers = 16  # 并行处理线程数

[stocks]
valid_prefixes = ["sz30", "sz00", "sh6", "bj", "sh880", "sh881", "sh000001", "sz399001", "bj899050"]
```

### 目录结构说明
```
data/
├── day_files/    # TDX day文件存放目录（init.py读取源）
├── downloads/    # 下载临时目录（update.py使用）
└── output/       # 按日期分区的parquet文件输出目录
```

### 3. 初始化转换

```bash
python init.py
```

**全新数据转换**：
- 程序会清理所有旧数据，确保数据完整性
- **自动并行下载**全量历史数据：
  - 上证日线 (shlday.zip)
  - 深证日线 (szlday.zip)
  - 北证日线 (bjlday.zip)
  - 指数日线 (tdxzs_day.zip)
- 使用4线程并行下载，显著提升下载速度
- 下载完成后自动解压并清理临时文件
- 然后转换为按日期分区的Parquet文件

**避免残缺数据**：
- 每次运行都会清理旧文件，防止程序中断导致的残缺数据影响
- 确保获取的是最新完整的历史数据集

### 5. 增量更新

```bash
python update.py
```

自动下载和处理新增的交易日数据（从最新日期到今天）。

## 文件格式

### 输入格式
- **TDX Day文件**：通达信标准的二进制格式（32字节/记录）
- 包含：日期、开高低收、成交量、成交额等字段

### 输出格式
- **按日期分区**：`tdx__YYYYMMDD.parquet`
- 每个文件包含该交易日的所有股票和指数数据
- 使用zstd压缩，压缩比约70%

### 数据字段
| 字段名 | 类型 | 描述 |
|--------|------|------|
| symbol | str | 证券代码 |
| date | date | 交易日期 |
| open | f32 | 开盘价 |
| high | f32 | 最高价 |
| low | f32 | 最低价 |
| close | f32 | 收盘价 |
| volume | u64 | 成交量 |
| amount | f64 | 成交额 |

## 项目结构

```
yqtdx/
├── init.py              # 初始化脚本
├── update.py            # 增量更新脚本
├── config.toml          # 配置文件
├── test_partitioned.py  # 测试脚本
├── tdx/
│   ├── dayfile.py      # TDX文件解析
│   └── converter.py    # 转换器核心
└── utils/
    └── config.py       # 配置管理
```

## 使用示例

### 读取单日数据
```python
import polars as pl
df = pl.read_parquet("data/parquet/tdx__20240101.parquet")
print(df.head())
```

### 读取指定日期范围
```python
from pathlib import Path
import polars as pl

# 读取2024年1月的所有数据
files = list(Path("data/parquet").glob("tdx__202401*.parquet"))
df = pl.concat([pl.read_parquet(f) for f in files])
```

### 分析特定股票
```python
# 读取某股票的历史数据
files = list(Path("data/parquet").glob("tdx__*.parquet"))
df = pl.concat([pl.read_parquet(f) for f in files])
stock_data = df.filter(pl.col("symbol") == "000001")
```

## 性能对比

| 方案 | 处理时间 | 存储大小 | 查询速度 |
|------|----------|----------|----------|
| 原始TDX | - | 100% | 慢 |
| CSV格式 | 慢 | 200% | 中等 |
| **Parquet+zstd** | **快** | **30%** | **极快** |

## 故障排除

### 1. 配置文件找不到
确保 `config.toml` 在脚本同目录下，或检查路径设置。

### 2. TDX数据路径错误
检查配置中的 `tdx_day_path` 是否指向正确的day文件目录。

### 3. 内存不足
减少 `n_jobs` 参数值，或增加系统内存。

### 4. 测试功能
运行测试脚本检查配置：
```bash
python test_partitioned.py
```

## 技术架构

- **解析器**：numpy structured arrays 高效读取二进制数据
- **处理器**：Polars DataFrame 提供极快的数据处理
- **存储**：Parquet + zstd 压缩优化存储和查询
- **并发**：joblib 多进程并行处理
- **分区**：按交易日分区，便于增量更新和查询

## 许可证

MIT License