import os
from pathlib import Path
from typing import List
from datetime import datetime
import polars as pl
from .dayfile import convert_all_day_files_individually
from utils.config import Config


class TDXConverter:
    """TDX数据转换器"""
    
    def __init__(self, config: Config):
        self.config = config
    
    
    def convert_to_individual_parquets(self, day_dir: str) -> str:
        """转换为按股票分别保存的独立Parquet文件"""
        output_dir = Path(self.config.output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        success_count = convert_all_day_files_individually(
            day_dir=day_dir,
            valid_prefixes=self.config.valid_prefixes,
            output_dir=str(output_dir),
            max_workers=self.config.max_workers
        )
        
        print(f"📊 成功转换了 {success_count} 个股票文件")
        return str(output_dir)
    
    def read_parquet_stats(self, parquet_path: str) -> dict:
        """读取Parquet文件统计信息"""
        if os.path.isfile(parquet_path):
            df = pl.read_parquet(parquet_path)
            return {
                'total_records': len(df),
                'symbols': df['symbol'].n_unique(),
                'date_range': (df['date'].min(), df['date'].max()),
                'file_size_mb': os.path.getsize(parquet_path) / 1024 / 1024
            }
        else:
            # 分区文件统计
            total_records = 0
            file_count = 0
            total_size = 0
            
            for file in Path(parquet_path).glob('*.parquet'):
                df = pl.read_parquet(str(file))
                total_records += len(df)
                file_count += 1
                total_size += os.path.getsize(file)
            
            return {
                'total_records': total_records,
                'file_count': file_count,
                'total_size_mb': total_size / 1024 / 1024
            }
    
    def get_latest_date_from_files(self, output_dir: str) -> datetime:
        """从文件名中获取最新日期"""
        output_path = Path(output_dir)
        
        if self.config.partitioned:
            # 分区模式：查找 tdx__YYYYMMDD.parquet 文件
            pattern = "tdx__????????.parquet"
            files = [f.stem for f in output_path.glob(pattern)]
            if not files:
                raise ValueError("No dated parquet files found")
            
            # 提取最新日期
            latest_file = max(files)
            date_str = latest_file.split('__')[1]
        else:
            # 单文件模式：需要读取文件内容获取最新日期
            parquet_file = output_path / "stocks_data.parquet"
            if not parquet_file.exists():
                raise ValueError("No parquet file found")
            
            df = pl.read_parquet(str(parquet_file))
            if df.is_empty():
                raise ValueError("Parquet file is empty")
            
            latest_date = df['date'].max()
            return latest_date
        
        # 解析日期
        try:
            return datetime.strptime(date_str, '%Y%m%d')
        except ValueError as e:
            raise ValueError(f"Invalid date format in filename: {date_str}") from e