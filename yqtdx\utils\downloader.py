"""
简化的TDX数据下载模块
遵循原项目的简单逻辑
"""
import requests
import zipfile
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import List


class TDXDownloader:
    """简化的TDX数据下载器，遵循原项目逻辑"""
    
    # 硬编码下载URL模板，与原项目保持一致
    BASE_URL_TEMPLATE = "https://www.tdx.com.cn/products/data/data/g4day/{date}.zip"
    
    def __init__(self, config):
        self.config = config
        self.download_dir = Path(config.download_dir)
        self.download_dir.mkdir(parents=True, exist_ok=True)
    
    def download_missing_dates(self, start_date: datetime, end_date: datetime) -> List[str]:
        """
        下载缺失日期的数据
        遵循原项目逻辑：从start_date逐日下载到end_date
        """
        downloaded_dates = []
        current_date = start_date
        
        print(f"🛠️  开始下载日线数据")
        
        while current_date <= end_date:
            date_str = current_date.strftime('%Y%m%d')
            success = self._download_single_date(date_str)
            if success:
                downloaded_dates.append(date_str)
            current_date += timedelta(days=1)
        
        if downloaded_dates:
            print(f"📥 成功下载了 {len(downloaded_dates)} 个交易日的数据")
        else:
            print("🌲 无需下载")
            
        return downloaded_dates
    
    def _download_single_date(self, date_str: str) -> bool:
        """下载单个日期的数据，完全遵循原项目逻辑"""
        url = self.BASE_URL_TEMPLATE.format(date=date_str)
        zip_path = self.download_dir / f"{date_str}.zip"
        
        try:
            # 简单的HTTP GET请求，与原项目一致
            response = requests.get(url, timeout=30)
            
            if response.status_code == 200:
                # 保存文件
                with open(zip_path, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ 成功下载 {date_str} 的数据")
                
                # 立即解压到TDX目录
                self._extract_zip(zip_path)
                
                # 删除zip文件（与原项目一致，不保留）
                zip_path.unlink()
                
                return True
                
            elif response.status_code == 404:
                print(f"🟡 {date_str} 非交易日或数据尚未更新")
                return False
            else:
                print(f"⚠️ 下载 {date_str} 失败: HTTP {response.status_code}")
                return False
                
        except requests.RequestException as e:
            print(f"⚠️ 下载 {date_str} 数据失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 处理 {date_str} 失败: {e}")
            return False
    
    def _extract_zip(self, zip_path: Path):
        """解压zip文件到TDX目录"""
        try:
            tdx_path = Path(self.config.tdx_day_path)
            tdx_path.mkdir(parents=True, exist_ok=True)
            
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(tdx_path)
                
        except Exception as e:
            print(f"❌ 解压失败: {e}")
            raise
    