name: Release with GoReleaser

on:
  push:
    tags:
      - 'v*' # 触发条件：推送到以 v 开头的标签（如 v1.0.0）

jobs:
  release:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # 获取完整历史以生成 changelog

      - name: Install unrar
        run: sudo apt-get update && sudo apt-get install -y unrar

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.21' # 替换为你使用的 Go 版本

      - name: Run GoReleaser
        uses: goreleaser/goreleaser-action@v6
        with:
          version: latest
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
